import { useIntervalFn, watchOnce } from '@peng_kai/kit/libs/vueuse';
import { customGet, getCookieValue, parseQueryParams, setCookie } from '~/utils';
import { oksOpenEven, setupOksScript } from './useOksPixel';

let notice: MetaBase | undefined;

export function useMeta() {
  const _localSource = sessionStorage.getItem('SOURCE');
  if (_localSource && window.source)
    return _localSource;
  const searchParams = parseQueryParams();
  const source = searchParams?.source || _localSource || getCookieValue('SOURCE') || '';
  sessionStorage.setItem('SOURCE', source);
  window.source = source;
  loadSourceInfo();
  const idState = ref(false);
  const { pause } = useIntervalFn(() => {
    if (window.posPlatform) {
      if (window.posPlatform === 1 && window.posId) { // facebook
        insertMetaPixel(window.posId);
        saveSourceToCookie(source);
      }
      if (window.posPlatform === 7) { // 块手
        kuaishouPlatform(source);
      }
      if (window.posPlatform === 6) { // TG
        saveSourceToCookie(source);
      }
      if (window.posPlatform === 8) { // oks
        setupOksScript();
      }
      idState.value = true;
    }
  }, 200);

  watchOnce(idState, () => pause());

  return source;
}

function insertMetaPixel(pixelId: string) {
  // const pixelId = '1681423272635523';
  // 创建并插入脚本元素
  const script = document.createElement('script');
  script.innerHTML = `
    !function(f,b,e,v,n,t,s)
  {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
  n.callMethod.apply(n,arguments):n.queue.push(arguments)};
  if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
  n.queue=[];t=b.createElement(e);t.async=!0;
  t.src=v;s=b.getElementsByTagName(e)[0];
  s.parentNode.insertBefore(t,s)}(window, document,'script',
  'https://connect.facebook.net/en_US/fbevents.js');
  fbq('init', '${pixelId}');
  `;
  document.head.appendChild(script);

  // 创建并插入 noscript 元素
  const noscript = document.createElement('noscript');
  noscript.innerHTML = `
    <img height="1" width="1" style="display:none"
      src="https://www.facebook.com/tr?id=${pixelId}&ev=PageView&noscript=1"
    />
  `;
  document.body.appendChild(noscript);
  noticeMeta().viewPage();
}

function kuaishouPlatform(source: string) {
  saveSourceToCookie(source);
}

function saveSourceToCookie(source: string) {
  setCookie('SOURCE', source);
}

async function loadSourceInfo() {
  try {
    const url = '/v1/init/handshake';
    const res = await customGet<{ code: number; data: HandshakeDetail; msg: string; request_id: string }>(url, window.source);
    console.log('res',res.data)
    window.posPlatform = res.data.promotion_channel?.platform;
    window.posId = res.data.promotion_channel?.pos_id;
    window.useCase = Number(res.data.promotion_channel?.use_case) || 0;
    window.isInstallAPK = window.useCase === 3;
    window.apkSrc = res.data.promotion_channel?.apk_url || '';
    window.gameDomain = res.data.domain?.www || '';
    window.countryCurrency = res.data.country_currency;
    window.cdn = res.data.domain?.cdn;
  }
  catch {}
}

interface HandshakeDetail {
  adjust_app_token: string;
  app_name: string;
  cf_site_key: string;
  country_currency: string;
  customer_type: number;
  domain: { agent: string; cdn: string; game: string; tab_game: string; verify: string; www: string };
  is_agent: number;
  login_method: { google: number; metamask: number; telegram: number; ton_connect: number; tronlink: number };
  promotion_channel?: { event_install_token: string; platform: number; pos_id: string;use_case: string;apk_url: string };
  session_id: string;
  telegram_bot: { username: string; webapp_name: string };
  telegram_business_customer_id: string;
  telegram_channel_id: string;
  telegram_customer_id: string;
  upgrade: number;
  webpush_public_key: string;
}

interface IRegisterParams {
  plateform: string;
  id: string;
}

interface IPayParams {
  currency: string;
  value: number;
}
class MetaBase {
  viewPage(): void {};
  register(params: IRegisterParams): void {};
  deposit(params: IPayParams): void {};
  withdraw(params: IPayParams): void {};
}

class FaceBook extends MetaBase {
  private isViewPage = false;
  viewPage(): void {
    if (this.isViewPage)
      return;
    window.fbq('track', 'PageView');
    this.isViewPage = true;
  }
}

class Oks extends MetaBase {
  private isOpen = false;
  viewPage(): void {
    if (window.isOks && !this.isOpen) {
      oksOpenEven();
      this.isOpen = true;
    }
  }
}

export function noticeMeta() {
  if (notice)
    return notice;
  let _notice: MetaBase | undefined;
  if (window.posPlatform === 1) {
    _notice = new FaceBook();
  }
  if (window.posPlatform === 8) {
    _notice = new Oks();
  }
  if (_notice)
    notice = _notice;
  return _notice || new MetaBase();
}
