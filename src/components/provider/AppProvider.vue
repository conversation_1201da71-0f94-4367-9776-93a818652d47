<script lang="ts">
import { legacyLogicalPropertiesTransformer, Spin } from 'ant-design-vue';
import { globalPopups } from '~comp/global-popups';
import { useMeta } from '~/composables/useMeta';
import { useHandshakeStore } from "~/stores";
import { useLocale } from '~/modules/locale';
import './helpers/index';
</script>

<script setup lang="ts">
const themeConfig = {};

Spin.setDefaultIndicator({
  indicator: h('i', { class: 'i-svg-spinners:ring-resize  text-white' }),
});

useLocale();
useMeta();
useHandshakeStore();
</script>

<template>
  <AConfigProvider hashPriority="high" :theme="themeConfig" :locale="JSON.parse(decodeURIComponent($t('antd')))">
    <AStyleProvider hashPriority="high" :transformers="[legacyLogicalPropertiesTransformer]">
      <component :is="Popup.PresetComponent" v-for="(Popup, name) of globalPopups" :key="name" />
      <slot />
    </AStyleProvider>
  </AConfigProvider>
</template>
