import { customGet } from '~/utils';

interface HandshakeDetail {
  adjust_app_token: string;
  app_name: string;
  cf_site_key: string;
  country_currency: string;
  customer_type: number;
  domain: { agent: string; cdn: string; game: string; tab_game: string; verify: string; www: string };
  is_agent: number;
  login_method: { google: number; metamask: number; telegram: number; ton_connect: number; tronlink: number };
  promotion_channel?: { event_install_token: string; platform: number; pos_id: string;use_case: string;apk_url: string };
  session_id: string;
  telegram_bot: { username: string; webapp_name: string };
  telegram_business_customer_id: string;
  telegram_channel_id: string;
  telegram_customer_id: string;
  upgrade: number;
  webpush_public_key: string;
}

export const useHandshakeStore = defineStore('currency', () => {
  const handshake = ref<HandshakeDetail>(undefined);

  const initHandshake = async () => {
    const url = '/v1/init/handshake';
    // const res = await customGet<{ code: number; data: HandshakeDetail; msg: string; request_id: string }>(url, window.source);
    handshake.value = {
      "app_name": "G9.Game",
      "session_id": "",
      "domain": {
        "www": "https://img.g9aaa.com",
        "cdn": "https://cdn.g9aaa.com",
        "agent": "ceshishunli666888999.vip",
        "game": "https://imgg.g9aaa.com",
        "verify": "https://g9official.github.io",
        "tab_game": "https://imgg.g9aaa.com"
      },
      "customer_type": 1,
      "telegram_customer_id": "https://t.me/G9game",
      "telegram_business_customer_id": "https://t.me/G9game",
      "telegram_channel_id": "https://t.me/G9game",
      "language": [
        {
          "id": 1,
          "name": "English",
          "code": "en_us",
          "emoji": "🇺🇸",
          "is_default": 1,
          "status": 1
        },
        {
          "id": 2,
          "name": "简体中文",
          "code": "zh_cn",
          "emoji": "🇨🇳",
          "is_default": 0,
          "status": 1
        },
        {
          "id": 3,
          "name": "繁體中文",
          "code": "zh_tw",
          "emoji": "🇭🇰",
          "is_default": 0,
          "status": 1
        },
        {
          "id": 4,
          "name": "Français",
          "code": "fr_fr",
          "emoji": "🇫🇷",
          "is_default": 0,
          "status": 1
        },
        {
          "id": 5,
          "name": "Indonesian",
          "code": "id_id",
          "emoji": "🇮🇩",
          "is_default": 0,
          "status": 1
        },
        {
          "id": 6,
          "name": "日本語",
          "code": "ja_jp",
          "emoji": "🇯🇵",
          "is_default": 0,
          "status": 1
        },
        {
          "id": 7,
          "name": "한국어",
          "code": "ko_kr",
          "emoji": "🇰🇷",
          "is_default": 0,
          "status": 1
        },
        {
          "id": 8,
          "name": "Español",
          "code": "es_es",
          "emoji": "🇪🇸",
          "is_default": 0,
          "status": 1
        },
        {
          "id": 9,
          "name": "Deutsch",
          "code": "de_de",
          "emoji": "🇩🇪",
          "is_default": 0,
          "status": 1
        },
        {
          "id": 10,
          "name": "русский",
          "code": "ru_ru",
          "emoji": "🇷🇺",
          "is_default": 0,
          "status": 1
        },
        {
          "id": 11,
          "name": "Україна",
          "code": "uk",
          "emoji": "🇺🇦",
          "is_default": 0,
          "status": 1
        },
        {
          "id": 12,
          "name": "Brasil",
          "code": "pt_br",
          "emoji": "🇧🇷",
          "is_default": 0,
          "status": 1
        }
      ],
      "telegram_bot": {
        "username": "ceshishunlibot",
        "webapp_name": "minigame"
      },
      "upgrade": 0,
      "cf_site_key": "0x4AAAAAAA36Yu3KUILvtKlg",
      "is_agent": 0,
      "country_currency": "SGD",
      "login_method": {
        "google": 1,
        "tronlink": 1,
        "metamask": 1,
        "telegram": 1,
        "ton_connect": 1
      },
      "adjust_app_token": "",
      "webpush_public_key": "BOoPlBtUtvChTom-n3XPOALuRC25tfXZ0CwWXql5HAuFIYz_ZhKjz-W-EvqgjiXg9F4pHMxFoKwb4uxQPWYzEoQ"
    };
  }

  initHandshake()

  return {
    handshake,
    initHandshake
  }
})