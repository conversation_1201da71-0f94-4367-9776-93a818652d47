import path from 'node:path';
import VueI18nPlugin from '@intlify/unplugin-vue-i18n/vite';
import vue from '@vitejs/plugin-vue';
import vueJsx from '@vitejs/plugin-vue-jsx';
// PostCSS
import autoprefixer from 'autoprefixer';
import postcssPresetEnv from 'postcss-preset-env';
import { visualizer } from 'rollup-plugin-visualizer';
import UnoCSS from 'unocss/vite';
import AutoImport from 'unplugin-auto-import/vite';
import { AntDesignVueResolver } from 'unplugin-vue-components/resolvers';
import Components from 'unplugin-vue-components/vite';
import { defineConfig, loadEnv, type Plugin } from 'vite';
import { VitePWA } from 'vite-plugin-pwa';

import tsAlias from 'vite-plugin-ts-alias';
import VueDevTools from 'vite-plugin-vue-devtools';

const ENV_DIR = './envs';

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, ENV_DIR) as ImportMetaEnv;
  return {
    envDir: ENV_DIR,
    server: {
      host: true,
      port: Number(env.VITE_PORT),
    },
    build: {
      target: ['chrome70', 'safari14', 'firefox68'],
      reportCompressedSize: true,
      minify: 'terser',
      // sourcemap: 'hidden',
      terserOptions: {
        compress: {
          drop_console: false,
          drop_debugger: true,
        },
      },
      rollupOptions: {
        input: {
          main: path.resolve('index.html'),
        },
        onwarn: (warning, warn) => {
          if (['CIRCULAR_DEPENDENCY', 'CYCLIC_CROSS_CHUNK_REEXPORT'].includes(warning.code!)) {
            return;
          }
          warn(warning);
        },
      },
    },
    plugins: [
      vue({
        template: {
          compilerOptions: {
            isCustomElement: tag => tag.startsWith('swiper-'),
          },
        },
      }),
      VitePWA({
        strategies: 'injectManifest',
        srcDir: 'src',
        filename: 'sw.ts',
        registerType: 'autoUpdate',
        injectRegister: false,

        // 禁用资源缓存
        pwaAssets: {
          disabled: true,
          // config: true,
        },
        manifest: false,

        injectManifest: {
          // globPatterns: ['**/*.{js,css,html,svg,png,ico}'],
          globPatterns: [],
          globIgnores: ['**/*'], // 忽略所有文件
          injectionPoint: undefined, // 禁用注入点
        },

        devOptions: {
          enabled: false,
          navigateFallback: 'index.html',
          suppressWarnings: true,
          type: 'module',
        },
      }),
      vueJsx(),
      UnoCSS({ inspector: true }),
      globalDefinePlugin(),
      visualizer(),
      VueDevTools(),
      tsAlias({ tsConfigName: 'tsconfig.app.json' }),
      AutoImport({
        dts: 'src/types/auto-imports.d.ts',
        vueTemplate: true,
        imports: ['vue', 'vue-router'],
        dirs: [path.resolve('src/auto-import')],
      }),
      Components({
        dts: 'src/types/components.d.ts',
        dirs: [],
        globs: [],
        resolvers: [
        // Antd 组件（AXxx）
          AntDesignVueResolver({ importStyle: false }),
          // 本项目组件（TXxx）
          {
            type: 'component' as const,
            resolve: name => name.match(/^T[A-Z]/) && { name: name.replace(/^T/, ''), from: '~/auto-import/components' },
          },
        ],
      }),
      VueI18nPlugin({
        runtimeOnly: true,
        compositionOnly: true,
        fullInstall: true,
        include: [path.resolve('src/modules/locale/**/appMessage.json')],
      }),
    ],
    css: {
      preprocessorOptions: {
        scss: {
          api: 'modern-compiler',
        },
      },
      postcss: {
        plugins: [
          autoprefixer() as any,
          postcssPresetEnv() as any,
        ],
      },
    },
  };
});

/**
 * 将 define 中以 __APP_ 开头的变量挂载到 window 对象上
 */
function globalDefinePlugin(): Plugin {
  let variables = {};
  return {
    name: 'vite-plugin-global-define',
    apply: 'build',
    configResolved(resolvedConfig) {
      const define = resolvedConfig.define;
      variables = Object.entries(define ?? {})
        .filter(([key]) => key.startsWith('__APP_'))
        .reduce((acc, [key, value]) => {
          acc[key] = value;
          return acc;
        }, {} as Record<string, string>);
    },
    transformIndexHtml(html) {
      const content = Object.entries(variables).map(([k, v]) => `window.${k} = ${v}`).join(';');
      return html.replace('<head>', `<head>\n<script>${content};</script>`);
    },
  };
}
